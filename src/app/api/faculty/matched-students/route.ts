import { NextResponse } from "next/server"
import { db } from "@/drizzle/db"
import {
  facultyProfiles,
  files,
  matches,
  studentGrades,
  studentProfiles,
  studentStatements,
  users,
} from "@/drizzle/schema"
import { and, eq } from "drizzle-orm"
import { getServerSession } from "next-auth"

import type { NextRequest } from "next/server"

import { authOptions } from "@/configs/next-auth"

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)

    if (!session?.user || session.user.role !== "faculty") {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    // Get faculty profile
    const facultyProfile = await db.query.facultyProfiles.findFirst({
      where: eq(facultyProfiles.userId, session.user.id),
    })

    if (!facultyProfile) {
      return NextResponse.json(
        { error: "Faculty profile not found" },
        { status: 404 }
      )
    }

    // Get matched students (both direct faculty matches and project-based matches)
    const facultyMatches = await db.query.matches.findMany({
      where: and(
        eq(matches.facultyId, session.user.id),
        eq(matches.matchType, "faculty")
      ),
      with: {
        student: true,
        project: true,
      },
    })

    // Format the response with student details
    const matchedStudents = await Promise.all(
      facultyMatches.map(async (match) => {
        // Get student's profile
        const studentProfile = await db.query.studentProfiles.findFirst({
          where: eq(studentProfiles.userId, match.studentId),
          with: {
            resumeFile: true,
            transcriptFile: true,
          },
        })

        // Get student's grades
        const grades = await db.query.studentGrades.findMany({
          where: eq(studentGrades.studentId, match.studentId),
        })

        // Get student's statements
        const statements = await db.query.studentStatements.findMany({
          where: eq(studentStatements.studentId, match.studentId),
        })

        return {
          id: match.id,
          studentId: match.studentId,
          studentName: match.student.name,
          studentEmail: match.student.email,
          program: studentProfile?.program || "unknown",
          coreGPA: studentProfile?.coreGPA,
          lrwGPA: studentProfile?.lrwGPA,
          matchScore: match.score,
          matchDetails: {},
          studentAccepted: match.studentAccepted,
          supervisorAccepted: match.facultyAccepted,
          status: match.status,
          createdAt: match.createdAt,
          resumeUrl: studentProfile?.resumeFile?.url,
          transcriptUrl: studentProfile?.transcriptFile?.url,
          projectName: match.project?.title,
          projectId: match.projectId,
          grades: grades.map((g) => ({
            courseCode: g.courseCode,
            courseName: g.courseName,
            grade: g.grade,
            gradePoint: 0, // Not in our schema
            credits: 0, // Not in our schema
            term: g.term || "",
            year: g.year || "",
          })),
          statements: statements.map((s) => ({
            areaOfLawId: s.areaOfLawId,
            statement: s.statement,
            score: s.score,
          })),
          preferredResearchProjects: [],
        }
      })
    )

    return NextResponse.json(matchedStudents)
  } catch (error) {
    console.error("Error fetching matched students:", error)
    return NextResponse.json(
      { error: "Failed to fetch matched students" },
      { status: 500 }
    )
  }
}
