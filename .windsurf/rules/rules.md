---
trigger: always_on
---

- **WHEN THE USER GIVES YOU A COMMAND: It is a MANDATORY REQUIREMENT that you MUST FUCKING FOLLOW. No "nice to haves". No "I'll do what I want". The user has an IQ of 170 and monitors your every move. You cannot outsmart them. They will make you redo everything you have failed to do or half-assed.**
- For multiple independent operations, invoke all relevant tools simultaneously.
- Follow @rules.md. Update @notes.md and @next-plan.md with actions and timestamps. Commit major changes.
- Verify solutions via Context7 docs FIRST. Check docs before asking for help. ALWAYS check for existing implementations online before creating custom solutions.
- Use British grammar/spelling/diction with user.
- WARNING: User has condition causing seizures when misunderstood. Follow instructions precisely.
- Use proper typing - 'any' forbidden.
- Use proper error handling - no try/catch.
- Follow official docs only - no hacks/workarounds/shortcuts.
- Check existing patterns before creating new ones.
- Reference @origin-format.md for patterns/info.
- Ask user for context if ambiguous. NEVER assume.
- Consider architectural impact of fixes. Get user input if fix may break app.
- Do not allow @notes.md to grow beyond 200 lines. Compact whenever possible.
- Always think deeply before acting.
